# Server Renewal and Analytics Script

This Python script automatically renews a free server and sends analytics data every 1 hour and 1 minute by making API calls to multiple endpoints.

## Features

- 🔄 Automatically renews server every 61 minutes (1 hour and 1 minute)
- 📊 Sends analytics pageview data to WispByte
- 📝 Comprehensive logging to both console and file
- 🛡️ Error handling and retry logic for both requests
- ⏰ Detailed scheduling information
- 🚀 Easy to run and monitor

## Installation

1. Install Python 3.6 or higher
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Run the script:
```bash
python server_renew.py
```

### Run in background (Windows):
```bash
# Using PowerShell
Start-Process python -ArgumentList "server_renew.py" -WindowStyle Hidden

# Or using nohup equivalent
python server_renew.py > renewal.log 2>&1 &
```

### Stop the script:
- Press `Ctrl+C` if running in foreground
- Kill the process if running in background

## Logs

The script creates two types of logs:
- **Console output**: Real-time status updates
- **server_renew.log**: Persistent log file with all activities

## Configuration

The script is pre-configured with:

### Server Renewal API:
- **URL**: `https://gpanel.eternalzero.cloud/api/client/freeservers/9e26fee7-e4cf-4440-bb2a-b2bd698a6075/renew`
- **Headers**: All necessary authentication and browser headers
- **Cookies**: Session cookies for authentication

### Analytics API:
- **URL**: `https://analytics.wispbyte.com/api/event`
- **Data**: Pageview event for WispByte dashboard
- **Headers**: Browser identification headers

### General:
- **Interval**: 61 minutes (1 hour and 1 minute) for both requests

## Important Notes

⚠️ **Authentication Tokens**: The script uses session cookies and XSRF tokens that may expire. If you start getting authentication errors, you may need to:
1. Log into the web panel in your browser
2. Capture new cookies and tokens using browser developer tools
3. Update the script with new values

## Troubleshooting

- **Connection errors**: Check your internet connection
- **Authentication errors**: Update cookies and tokens
- **Permission errors**: Run with appropriate permissions
- **Port conflicts**: Ensure no other processes are using the same resources

## Security

- Keep your authentication tokens secure
- Don't share the script with embedded tokens
- Consider using environment variables for sensitive data in production
